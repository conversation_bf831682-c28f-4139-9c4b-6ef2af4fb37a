# Chatbot Service - Implementation Guide

## 🚀 Step-by-Step Implementation

### Step 1: Create Basic Service Structure

```bash
# Create chatbot service directory
mkdir chatbot-service
cd chatbot-service

# Initialize npm project
npm init -y

# Install dependencies
npm install express socket.io @google/genai amqplib redis jsonwebtoken cors helmet express-rate-limit joi winston dotenv mysql2 sequelize

# Install dev dependencies  
npm install --save-dev nodemon jest eslint supertest

# Create directory structure
mkdir -p src/{config,controllers,services,middleware,models,routes,utils,websocket}
mkdir -p tests/{unit,integration}
mkdir logs
```

### Step 2: Core Configuration Files

#### A. Main App Configuration
```javascript
// src/config/index.js
require('dotenv').config();

const config = {
  // Server Configuration
  port: process.env.PORT || 3006,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // Service URLs
  services: {
    auth: process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
    archive: process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002',
    assessment: process.env.ASSESSMENT_SERVICE_URL || 'http://localhost:3003'
  },
  
  // Security
  jwt: {
    secret: process.env.JWT_SECRET || 'atma_secure_jwt_secret_key'
  },
  
  internalServiceKey: process.env.INTERNAL_SERVICE_KEY || 'internal_service_secret_key',
  
  // AI Configuration
  ai: {
    apiKey: process.env.GOOGLE_AI_API_KEY,
    model: process.env.GOOGLE_AI_MODEL || 'gemini-2.5-flash',
    temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
    maxTokens: parseInt(process.env.AI_MAX_TOKENS || '500')
  },
  
  // RabbitMQ Configuration
  rabbitmq: {
    url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
    eventsExchange: process.env.EVENTS_EXCHANGE_NAME || 'atma_events_exchange',
    chatbotQueue: process.env.EVENTS_QUEUE_NAME_CHATBOT || 'analysis_events_chatbot'
  },
  
  // Redis Configuration
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379'
  },
  
  // Rate Limiting
  rateLimiting: {
    windowMs: parseInt(process.env.CHAT_RATE_LIMIT_WINDOW || '60000'),
    max: parseInt(process.env.CHAT_RATE_LIMIT_MAX || '30')
  },
  
  // CORS
  cors: {
    origin: process.env.CORS_ORIGIN || '*'
  }
};

module.exports = config;
```

#### B. AI Service Configuration
```javascript
// src/config/ai.js
const { GoogleGenAI } = require('@google/genai');
const config = require('./index');
const logger = require('../utils/logger');

let client = null;

const initialize = () => {
  try {
    if (!config.ai.apiKey) {
      throw new Error('Google AI API key is required');
    }
    
    client = new GoogleGenAI(config.ai.apiKey);
    logger.info('AI service initialized', { model: config.ai.model });
  } catch (error) {
    logger.error('Failed to initialize AI service', { error: error.message });
    throw error;
  }
};

const getClient = () => {
  if (!client) {
    throw new Error('AI service not initialized');
  }
  return client;
};

module.exports = {
  initialize,
  getClient,
  config: config.ai
};
```

### Step 3: Core Services Implementation

#### A. Chat Service
```javascript
// src/services/chatService.js
const aiService = require('./aiService');
const personaService = require('./personaService');
const contextService = require('./contextService');
const logger = require('../utils/logger');

class ChatService {
  async processMessage(userId, conversationId, message) {
    const startTime = Date.now();
    
    try {
      logger.info('Processing chat message', { userId, conversationId, messageLength: message.length });
      
      // Validate input
      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }
      
      if (message.length > 1000) {
        throw new Error('Message too long (max 1000 characters)');
      }
      
      // Get user's persona data (with caching)
      const personaData = await personaService.getUserPersona(userId);
      
      // Get conversation context
      const context = await contextService.getContext(conversationId, 10); // Last 10 messages
      
      // Generate AI response
      const response = await aiService.generateChatResponse(message, context, personaData);
      
      // Save message and response
      await contextService.addMessage(conversationId, {
        role: 'user',
        content: message.trim(),
        timestamp: new Date()
      });
      
      await contextService.addMessage(conversationId, {
        role: 'assistant',
        content: response,
        timestamp: new Date()
      });
      
      const processingTime = Date.now() - startTime;
      
      logger.info('Chat message processed successfully', {
        userId,
        conversationId,
        processingTime,
        responseLength: response.length,
        hasPersona: !!personaData
      });
      
      return {
        success: true,
        response,
        conversationId,
        timestamp: new Date(),
        processingTime
      };
      
    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      logger.error('Failed to process chat message', {
        userId,
        conversationId,
        error: error.message,
        processingTime
      });
      
      // Return appropriate error response
      return this.getErrorResponse(error, conversationId);
    }
  }
  
  getErrorResponse(error, conversationId) {
    let message = "Maaf, saya mengalami kesulitan teknis. Silakan coba lagi dalam beberapa saat.";
    
    if (error.message.includes('empty')) {
      message = "Pesan tidak boleh kosong. Silakan kirim pesan yang valid.";
    } else if (error.message.includes('too long')) {
      message = "Pesan terlalu panjang. Silakan kirim pesan yang lebih singkat (maksimal 1000 karakter).";
    } else if (error.message.includes('rate limit')) {
      message = "Anda mengirim pesan terlalu cepat. Silakan tunggu sebentar sebelum mengirim pesan lagi.";
    }
    
    return {
      success: false,
      response: message,
      conversationId,
      timestamp: new Date(),
      error: true
    };
  }
  
  async createConversation(userId, title = null) {
    try {
      const conversationId = `conv_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      await contextService.createConversation(conversationId, userId, title);
      
      logger.info('New conversation created', { userId, conversationId });
      
      return {
        success: true,
        conversationId,
        title: title || 'Career Guidance Chat',
        createdAt: new Date()
      };
      
    } catch (error) {
      logger.error('Failed to create conversation', { userId, error: error.message });
      throw error;
    }
  }
  
  async getConversationHistory(userId, conversationId, limit = 50) {
    try {
      const messages = await contextService.getMessages(conversationId, limit);
      
      // Verify user owns this conversation
      const conversation = await contextService.getConversation(conversationId);
      if (conversation.userId !== userId) {
        throw new Error('Unauthorized access to conversation');
      }
      
      return {
        success: true,
        conversationId,
        messages,
        total: messages.length
      };
      
    } catch (error) {
      logger.error('Failed to get conversation history', { 
        userId, 
        conversationId, 
        error: error.message 
      });
      throw error;
    }
  }
  
  async endConversation(userId, conversationId) {
    try {
      await contextService.endConversation(conversationId, userId);
      
      logger.info('Conversation ended', { userId, conversationId });
      
      return {
        success: true,
        message: 'Conversation ended successfully'
      };
      
    } catch (error) {
      logger.error('Failed to end conversation', { 
        userId, 
        conversationId, 
        error: error.message 
      });
      throw error;
    }
  }
}

module.exports = new ChatService();
```

#### B. AI Service for Chat
```javascript
// src/services/aiService.js
const ai = require('../config/ai');
const logger = require('../utils/logger');
const cacheService = require('./cacheService');

class ChatAIService {
  async generateChatResponse(message, context, personaData) {
    try {
      // Check cache first
      const cacheKey = this.generateCacheKey(message, context, personaData);
      const cachedResponse = await cacheService.getCachedResponse(cacheKey);
      
      if (cachedResponse) {
        logger.debug('Using cached AI response', { cacheKey });
        return cachedResponse;
      }
      
      const client = ai.getClient();
      const prompt = this.buildChatPrompt(message, context, personaData);
      
      const response = await client.models.generateContent({
        model: ai.config.model,
        contents: prompt,
        config: {
          temperature: ai.config.temperature,
          maxTokens: ai.config.maxTokens,
          systemInstruction: this.getSystemInstruction()
        }
      });
      
      const responseText = response.text.trim();
      
      // Cache the response
      await cacheService.cacheResponse(cacheKey, responseText, 1800); // 30 minutes
      
      logger.debug('AI response generated', { 
        messageLength: message.length,
        responseLength: responseText.length,
        hasPersona: !!personaData
      });
      
      return responseText;
      
    } catch (error) {
      logger.error('Failed to generate AI response', { error: error.message });
      
      // Return fallback response
      return this.getFallbackResponse(personaData);
    }
  }
  
  buildChatPrompt(message, context, personaData) {
    let prompt = `Anda adalah AI Career Counselor yang membantu pengguna dengan pertanyaan karir.\n\n`;
    
    // Add persona context if available
    if (personaData) {
      prompt += `INFORMASI PERSONA PENGGUNA:\n`;
      prompt += `- Archetype: ${personaData.archetype}\n`;
      prompt += `- Kekuatan: ${personaData.strengths?.join(', ') || 'Tidak tersedia'}\n`;
      prompt += `- Area Pengembangan: ${personaData.weaknesses?.join(', ') || 'Tidak tersedia'}\n`;
      prompt += `- Rekomendasi Karir: ${personaData.careerRecommendation?.map(c => c.careerName).join(', ') || 'Tidak tersedia'}\n\n`;
    } else {
      prompt += `CATATAN: Data persona pengguna belum tersedia. Berikan saran umum yang bermanfaat.\n\n`;
    }
    
    // Add conversation context
    if (context && context.length > 0) {
      prompt += `KONTEKS PERCAKAPAN SEBELUMNYA:\n`;
      context.slice(-5).forEach(msg => {
        prompt += `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}\n`;
      });
      prompt += `\n`;
    }
    
    prompt += `PESAN PENGGUNA SAAT INI: ${message}\n\n`;
    prompt += `Berikan respons yang:\n`;
    prompt += `1. Personal dan relevan dengan persona pengguna (jika tersedia)\n`;
    prompt += `2. Actionable dan praktis\n`;
    prompt += `3. Mendorong pengembangan karir\n`;
    prompt += `4. Maksimal 3 paragraf\n`;
    prompt += `5. Menggunakan bahasa Indonesia yang natural dan ramah\n`;
    
    return prompt;
  }
  
  getSystemInstruction() {
    return `Anda adalah AI Career Counselor yang ahli dalam memberikan bimbingan karir. 
Anda membantu pengguna berdasarkan hasil analisis persona mereka dari asesmen psikometrik (RIASEC, OCEAN, VIA-IS).
Gaya komunikasi Anda ramah, supportive, dan memberikan saran yang actionable.
Selalu fokus pada pengembangan karir dan potensi pengguna.`;
  }
  
  generateCacheKey(message, context, personaData) {
    const crypto = require('crypto');
    const data = {
      message: message.toLowerCase().trim(),
      contextLength: context?.length || 0,
      hasPersona: !!personaData,
      archetype: personaData?.archetype || null
    };
    
    return crypto.createHash('md5').update(JSON.stringify(data)).digest('hex');
  }
  
  getFallbackResponse(personaData) {
    if (personaData) {
      return `Berdasarkan profil ${personaData.archetype} Anda, saya merekomendasikan untuk fokus pada pengembangan kekuatan utama Anda. Namun saat ini saya mengalami kendala teknis. Silakan coba tanyakan kembali atau hubungi konselor karir kami untuk bantuan lebih lanjut.`;
    }
    
    return `Saya siap membantu Anda dengan pertanyaan karir, namun saat ini mengalami kendala teknis. Silakan coba lagi dalam beberapa saat atau hubungi konselor karir kami untuk bantuan langsung.`;
  }
}

module.exports = new ChatAIService();
```

### Step 4: API Routes Implementation

```javascript
// src/routes/chat.js
const express = require('express');
const chatController = require('../controllers/chatController');
const { verifyToken } = require('../middleware/auth');
const { validateMessage, validateConversation } = require('../middleware/validation');
const { chatLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// All chat routes require authentication
router.use(verifyToken);

// Apply rate limiting to all chat routes
router.use(chatLimiter);

// Conversation management
router.post('/conversations', validateConversation, chatController.createConversation);
router.get('/conversations/:conversationId', chatController.getConversation);
router.delete('/conversations/:conversationId', chatController.endConversation);

// Message handling
router.post('/conversations/:conversationId/messages', validateMessage, chatController.sendMessage);
router.get('/conversations/:conversationId/messages', chatController.getMessages);

// User's conversations list
router.get('/conversations', chatController.getUserConversations);

module.exports = router;
```

```javascript
// src/controllers/chatController.js
const chatService = require('../services/chatService');
const logger = require('../utils/logger');

class ChatController {
  async createConversation(req, res) {
    try {
      const { title } = req.body;
      const userId = req.user.userId;
      
      const result = await chatService.createConversation(userId, title);
      
      res.status(201).json({
        success: true,
        message: 'Conversation created successfully',
        data: result
      });
      
    } catch (error) {
      logger.error('Create conversation error', { 
        userId: req.user.userId, 
        error: error.message 
      });
      
      res.status(500).json({
        success: false,
        error: {
          code: 'CONVERSATION_CREATE_FAILED',
          message: 'Failed to create conversation'
        }
      });
    }
  }
  
  async sendMessage(req, res) {
    try {
      const { message } = req.body;
      const { conversationId } = req.params;
      const userId = req.user.userId;
      
      const result = await chatService.processMessage(userId, conversationId, message);
      
      if (result.success) {
        res.json({
          success: true,
          message: 'Message processed successfully',
          data: result
        });
      } else {
        res.status(400).json({
          success: false,
          error: {
            code: 'MESSAGE_PROCESSING_FAILED',
            message: result.response
          }
        });
      }
      
    } catch (error) {
      logger.error('Send message error', { 
        userId: req.user.userId, 
        conversationId: req.params.conversationId,
        error: error.message 
      });
      
      res.status(500).json({
        success: false,
        error: {
          code: 'MESSAGE_SEND_FAILED',
          message: 'Failed to send message'
        }
      });
    }
  }
  
  async getConversation(req, res) {
    try {
      const { conversationId } = req.params;
      const userId = req.user.userId;
      
      const result = await chatService.getConversationHistory(userId, conversationId);
      
      res.json({
        success: true,
        message: 'Conversation retrieved successfully',
        data: result
      });
      
    } catch (error) {
      logger.error('Get conversation error', { 
        userId: req.user.userId, 
        conversationId: req.params.conversationId,
        error: error.message 
      });
      
      const statusCode = error.message.includes('Unauthorized') ? 403 : 500;
      
      res.status(statusCode).json({
        success: false,
        error: {
          code: statusCode === 403 ? 'UNAUTHORIZED_ACCESS' : 'CONVERSATION_FETCH_FAILED',
          message: statusCode === 403 ? 'Access denied' : 'Failed to retrieve conversation'
        }
      });
    }
  }
  
  async endConversation(req, res) {
    try {
      const { conversationId } = req.params;
      const userId = req.user.userId;
      
      const result = await chatService.endConversation(userId, conversationId);
      
      res.json({
        success: true,
        message: 'Conversation ended successfully',
        data: result
      });
      
    } catch (error) {
      logger.error('End conversation error', { 
        userId: req.user.userId, 
        conversationId: req.params.conversationId,
        error: error.message 
      });
      
      res.status(500).json({
        success: false,
        error: {
          code: 'CONVERSATION_END_FAILED',
          message: 'Failed to end conversation'
        }
      });
    }
  }
}

module.exports = new ChatController();
```

## 🔧 Next Steps

1. **Implement remaining services** (personaService, contextService, cacheService)
2. **Setup WebSocket handlers** for real-time chat
3. **Configure RabbitMQ event consumers** for persona updates
4. **Add comprehensive testing** (unit + integration tests)
5. **Setup monitoring and logging**
6. **Deploy and integrate** with existing ATMA infrastructure

Apakah Anda ingin saya lanjutkan dengan implementasi service yang tersisa atau ada bagian tertentu yang ingin diprioritaskan?
