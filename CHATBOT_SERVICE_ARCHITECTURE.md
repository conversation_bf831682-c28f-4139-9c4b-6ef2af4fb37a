# Chatbot Service Architecture - ATMA Backend

## 📋 Overview

Dokumen ini menjelaskan arsitektur dan implementasi **Chatbot Service** untuk sistem ATMA (AI-Driven Talent Mapping Assessment). Chatbot service akan menyediakan layanan percakapan AI yang terintegrasi dengan hasil analisis persona dan dapat berkomunikasi dengan layanan AI yang sudah ada.

## 🏗️ Arsitektur Saat Ini

### Microservices yang Sudah Ada
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │   Auth Service  │    │ Archive Service │
│    Port: 3000   │    │   Port: 3001    │    │   Port: 3002    │
└─────────────────┘    └─────────────────┘    └─────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│Assessment Service│   │Notification Svc │    │ Analysis Worker │
│   Port: 3003    │    │   Port: 3005    │    │  (Background)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Event-Driven Architecture dengan RabbitMQ
- **Exchange**: `atma_events_exchange` (topic)
- **Routing Keys**: 
  - `analysis.completed`
  - `analysis.failed` 
  - `analysis.started`
- **Queues**: Service-specific event queues

## 🤖 Chatbot Service - Arsitektur yang Diusulkan

### Option 1: Standalone Chatbot Service (Recommended)

```
┌─────────────────────────────────────────────────────────────────┐
│                        CHATBOT SERVICE                         │
│                        Port: 3006                              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Chat API      │  │  Conversation   │  │   AI Service    │ │
│  │   Controller    │  │    Manager      │  │   Integration   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   WebSocket     │  │   Context       │  │   Persona       │ │
│  │   Handler       │  │   Service       │  │   Integration   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      INTEGRATION LAYER                         │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   RabbitMQ      │  │   Archive       │  │   Analysis      │ │
│  │   Events        │  │   Service       │  │   Worker AI     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Option 2: Extend Analysis Worker (Alternative)

Memperluas `analysis-worker/src/services/aiService.js` dengan fungsi chatbot, namun ini tidak direkomendasikan karena:
- **Single Responsibility Principle**: Analysis Worker fokus pada batch processing
- **Scalability**: Chatbot membutuhkan real-time response
- **Resource Management**: Berbeda kebutuhan resource dan concurrency

## 🚀 Implementasi Chatbot Service

### 1. Service Structure

```
chatbot-service/
├── src/
│   ├── app.js                 # Express app setup
│   ├── server.js              # Server startup
│   ├── config/
│   │   ├── index.js           # Main configuration
│   │   ├── ai.js              # AI service config
│   │   ├── rabbitmq.js        # RabbitMQ config
│   │   └── database.js        # Database config (optional)
│   ├── controllers/
│   │   ├── chatController.js  # Chat API endpoints
│   │   └── healthController.js
│   ├── services/
│   │   ├── chatService.js     # Core chat logic
│   │   ├── aiService.js       # AI integration
│   │   ├── contextService.js  # Conversation context
│   │   ├── personaService.js  # Persona integration
│   │   └── eventConsumer.js   # RabbitMQ events
│   ├── middleware/
│   │   ├── auth.js            # Authentication
│   │   ├── rateLimiter.js     # Rate limiting
│   │   └── validation.js      # Input validation
│   ├── models/
│   │   └── conversation.js    # Conversation model
│   ├── routes/
│   │   ├── chat.js            # Chat routes
│   │   └── health.js          # Health routes
│   ├── utils/
│   │   ├── logger.js          # Logging utility
│   │   └── errors.js          # Error handling
│   └── websocket/
│       └── chatSocket.js      # WebSocket handler
├── package.json
├── .env.example
└── README.md
```

### 2. Core Features

#### A. Chat API Endpoints
```javascript
// REST API
POST   /api/chat/conversations          # Start new conversation
GET    /api/chat/conversations/:id      # Get conversation history
POST   /api/chat/conversations/:id/messages  # Send message
DELETE /api/chat/conversations/:id      # End conversation

// WebSocket
WS     /chat                           # Real-time chat
```

#### B. AI Integration Strategy

**Reuse Existing AI Service:**
```javascript
// chatbot-service/src/services/aiService.js
const { GoogleGenAI } = require('@google/genai');

class ChatbotAIService {
  constructor() {
    // Reuse configuration from analysis-worker
    this.client = new GoogleGenAI(process.env.GOOGLE_AI_API_KEY);
    this.model = process.env.GOOGLE_AI_MODEL || 'gemini-2.5-flash';
  }

  async generateChatResponse(message, context, personaData) {
    const prompt = this.buildChatPrompt(message, context, personaData);
    
    const response = await this.client.models.generateContent({
      model: this.model,
      contents: prompt,
      config: {
        temperature: 0.7, // More conversational
        maxTokens: 500    // Limit for chat responses
      }
    });

    return response.text;
  }

  buildChatPrompt(message, context, personaData) {
    return `
Anda adalah AI Career Counselor yang membantu pengguna berdasarkan hasil analisis persona mereka.

PERSONA DATA:
${JSON.stringify(personaData, null, 2)}

CONVERSATION CONTEXT:
${context.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

USER MESSAGE: ${message}

Berikan respons yang:
1. Personal dan relevan dengan persona pengguna
2. Actionable dan praktis
3. Mendorong pengembangan karir
4. Maksimal 3 paragraf
`;
  }
}
```

### 3. Integration dengan Existing Services

#### A. Persona Integration
```javascript
// chatbot-service/src/services/personaService.js
class PersonaService {
  async getUserPersona(userId) {
    try {
      // Call Archive Service untuk mendapatkan hasil analisis terbaru
      const response = await fetch(`${process.env.ARCHIVE_SERVICE_URL}/api/v1/results/user/${userId}/latest`, {
        headers: {
          'X-Internal-Service': 'true',
          'X-Service-Key': process.env.INTERNAL_SERVICE_KEY
        }
      });

      if (response.ok) {
        const data = await response.json();
        return data.data.personaProfile;
      }
      
      return null;
    } catch (error) {
      logger.error('Failed to fetch user persona', { userId, error: error.message });
      return null;
    }
  }
}
```

#### B. Event-Driven Updates
```javascript
// chatbot-service/src/services/eventConsumer.js
class ChatbotEventConsumer {
  async processEvent(eventData) {
    switch (eventData.eventType) {
      case 'analysis.completed':
        // Update user's persona context untuk chat sessions yang aktif
        await this.updateActiveConversations(eventData.userId, eventData.resultId);
        break;
        
      case 'analysis.started':
        // Notify user via chat jika mereka sedang online
        await this.notifyAnalysisStarted(eventData.userId, eventData.jobId);
        break;
    }
  }
}
```

### 4. Scalability Considerations

#### A. Horizontal Scaling
```yaml
# docker-compose.yml
services:
  chatbot-service-1:
    build: ./chatbot-service
    ports:
      - "3006:3006"
    environment:
      - INSTANCE_ID=chatbot-1
      
  chatbot-service-2:
    build: ./chatbot-service
    ports:
      - "3007:3006"
    environment:
      - INSTANCE_ID=chatbot-2

  nginx-chatbot-lb:
    image: nginx
    ports:
      - "3006:80"
    # Load balancer configuration
```

#### B. Session Management
```javascript
// Menggunakan Redis untuk shared session storage
const redis = require('redis');
const client = redis.createClient(process.env.REDIS_URL);

class ConversationManager {
  async saveConversation(conversationId, data) {
    await client.setex(`conversation:${conversationId}`, 3600, JSON.stringify(data));
  }

  async getConversation(conversationId) {
    const data = await client.get(`conversation:${conversationId}`);
    return data ? JSON.parse(data) : null;
  }
}
```

#### C. Rate Limiting & Resource Management
```javascript
// Specific rate limiting untuk chat
const chatLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // 30 messages per minute per user
  message: 'Too many messages, please slow down'
});

// Token usage tracking
class ChatTokenTracker {
  async trackChatUsage(userId, tokens) {
    // Track chat-specific token usage
    // Separate dari analysis token usage
  }
}
```

## 🔄 Message Flow

### 1. User Sends Message
```
User → API Gateway → Chatbot Service → AI Service → Response
  ↓
WebSocket notification → User
```

### 2. Context-Aware Response
```
Message → Context Service → Persona Service → AI Service
                ↓
         Enhanced Prompt → Google AI → Personalized Response
```

### 3. Event Integration
```
Analysis Completed → RabbitMQ → Chatbot Event Consumer → Update Active Chats
```

## 📊 Monitoring & Analytics

### 1. Metrics to Track
- **Response Time**: Average AI response time
- **User Engagement**: Messages per session, session duration
- **Token Usage**: Chat-specific token consumption
- **Error Rates**: Failed AI calls, timeout rates

### 2. Logging Strategy
```javascript
// Structured logging untuk chat
logger.info('Chat message processed', {
  userId,
  conversationId,
  messageLength: message.length,
  responseTime,
  tokensUsed,
  personaAvailable: !!personaData
});
```

## 🔒 Security Considerations

### 1. Authentication & Authorization
- Reuse JWT token dari Auth Service
- Validate user access untuk conversation history
- Rate limiting per user

### 2. Data Privacy
- Encrypt conversation data at rest
- Automatic conversation cleanup (30 days)
- No sensitive data dalam logs

### 3. AI Safety
- Content filtering untuk inappropriate messages
- Response validation
- Fallback responses untuk AI failures

## 🚀 Deployment Strategy

### Phase 1: MVP (2-3 weeks)
- Basic chat API dengan persona integration
- Simple WebSocket support
- Integration dengan existing AI service

### Phase 2: Enhanced Features (2-3 weeks)
- Advanced conversation context
- Event-driven updates
- Comprehensive monitoring

### Phase 3: Scale & Optimize (1-2 weeks)
- Horizontal scaling
- Performance optimization
- Advanced analytics

## 📝 Next Steps

1. **Setup Development Environment**
   ```bash
   mkdir chatbot-service
   cd chatbot-service
   npm init -y
   npm install express socket.io @google/genai amqplib redis
   ```

2. **Create Basic Service Structure**
   - Implement core chat API
   - Setup RabbitMQ integration
   - Configure AI service connection

3. **Integration Testing**
   - Test dengan existing services
   - Validate persona data integration
   - Performance testing

4. **Production Deployment**
   - Setup monitoring
   - Configure load balancing
   - Deploy dengan existing infrastructure

## 🤔 Rekomendasi

**Gunakan Standalone Chatbot Service** karena:
- ✅ **Separation of Concerns**: Chat dan analysis adalah domain berbeda
- ✅ **Scalability**: Dapat di-scale independent
- ✅ **Maintainability**: Easier to maintain dan develop
- ✅ **Performance**: Optimized untuk real-time chat
- ✅ **Reusability**: AI service dapat digunakan untuk kedua use case

**Leverage Existing Infrastructure**:
- ✅ Reuse Google AI configuration dan service
- ✅ Integrate dengan RabbitMQ event system
- ✅ Use existing authentication dan authorization
- ✅ Follow established patterns dan conventions

## 💻 Implementation Examples

### 1. Core Chat Service Implementation

```javascript
// chatbot-service/src/services/chatService.js
const aiService = require('./aiService');
const personaService = require('./personaService');
const contextService = require('./contextService');
const logger = require('../utils/logger');

class ChatService {
  async processMessage(userId, conversationId, message) {
    try {
      // Get user's persona data
      const personaData = await personaService.getUserPersona(userId);

      // Get conversation context
      const context = await contextService.getContext(conversationId);

      // Generate AI response
      const response = await aiService.generateChatResponse(
        message,
        context,
        personaData
      );

      // Save message and response to context
      await contextService.addMessage(conversationId, {
        role: 'user',
        content: message,
        timestamp: new Date()
      });

      await contextService.addMessage(conversationId, {
        role: 'assistant',
        content: response,
        timestamp: new Date()
      });

      logger.info('Chat message processed', {
        userId,
        conversationId,
        messageLength: message.length,
        responseLength: response.length,
        hasPersona: !!personaData
      });

      return {
        response,
        conversationId,
        timestamp: new Date()
      };

    } catch (error) {
      logger.error('Failed to process chat message', {
        userId,
        conversationId,
        error: error.message
      });

      // Return fallback response
      return {
        response: "Maaf, saya mengalami kesulitan teknis. Silakan coba lagi dalam beberapa saat.",
        conversationId,
        timestamp: new Date(),
        error: true
      };
    }
  }
}

module.exports = new ChatService();
```

### 2. WebSocket Integration

```javascript
// chatbot-service/src/websocket/chatSocket.js
const socketIo = require('socket.io');
const jwt = require('jsonwebtoken');
const chatService = require('../services/chatService');
const logger = require('../utils/logger');

class ChatSocketHandler {
  constructor(server) {
    this.io = socketIo(server, {
      cors: {
        origin: process.env.CORS_ORIGIN || "*",
        methods: ["GET", "POST"]
      }
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  setupMiddleware() {
    // JWT Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        socket.userId = decoded.userId;
        socket.userRole = decoded.role;
        next();
      } catch (error) {
        next(new Error('Authentication failed'));
      }
    });
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      logger.info('User connected to chat', {
        userId: socket.userId,
        socketId: socket.id
      });

      // Join user-specific room
      socket.join(`user:${socket.userId}`);

      // Handle chat messages
      socket.on('chat:message', async (data) => {
        try {
          const { conversationId, message } = data;

          // Process message
          const result = await chatService.processMessage(
            socket.userId,
            conversationId,
            message
          );

          // Send response back to user
          socket.emit('chat:response', result);

        } catch (error) {
          socket.emit('chat:error', {
            message: 'Failed to process message',
            error: error.message
          });
        }
      });

      // Handle conversation start
      socket.on('chat:start', async (data) => {
        try {
          const conversationId = `conv_${socket.userId}_${Date.now()}`;

          socket.emit('chat:started', {
            conversationId,
            message: 'Halo! Saya siap membantu Anda dengan pertanyaan seputar karir berdasarkan hasil analisis persona Anda.'
          });

        } catch (error) {
          socket.emit('chat:error', {
            message: 'Failed to start conversation'
          });
        }
      });

      socket.on('disconnect', () => {
        logger.info('User disconnected from chat', {
          userId: socket.userId,
          socketId: socket.id
        });
      });
    });
  }

  // Method to send notifications to specific user
  sendToUser(userId, event, data) {
    this.io.to(`user:${userId}`).emit(event, data);
  }
}

module.exports = ChatSocketHandler;
```

### 3. API Gateway Integration

```javascript
// api-gateway/src/routes/index.js (addition)

// ===== CHATBOT SERVICE ROUTES =====
const { chatbotServiceProxy } = require('../middleware/proxy');

// Protected chatbot endpoints
router.use('/chat/conversations', verifyToken, chatLimiter, chatbotServiceProxy);
router.use('/chat/messages', verifyToken, chatLimiter, chatbotServiceProxy);

// WebSocket proxy for chat
router.use('/chat/socket.io/*', chatbotServiceProxy);

// Fallback for chatbot routes
router.use('/chat/*', chatbotServiceProxy);
```

```javascript
// api-gateway/src/middleware/proxy.js (addition)
const { createProxyMiddleware } = require('http-proxy-middleware');

const chatbotServiceProxy = createProxyMiddleware({
  target: process.env.CHATBOT_SERVICE_URL || 'http://localhost:3006',
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying
  pathRewrite: {
    '^/api/chat': '/api/chat'
  },
  onError: (err, req, res) => {
    logger.error('Chatbot service proxy error', {
      error: err.message,
      url: req.url
    });
    res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Chatbot service is currently unavailable'
      }
    });
  }
});

module.exports = {
  // ... existing proxies
  chatbotServiceProxy
};
```

### 4. Database Schema (Optional - untuk persistent conversations)

```sql
-- chatbot_conversations table
CREATE TABLE chatbot_conversations (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    title VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    metadata JSON,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- chatbot_messages table
CREATE TABLE chatbot_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    conversation_id VARCHAR(255) NOT NULL,
    role ENUM('user', 'assistant') NOT NULL,
    content TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSON,
    FOREIGN KEY (conversation_id) REFERENCES chatbot_conversations(id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_timestamp (timestamp)
);
```

### 5. Environment Configuration

```bash
# chatbot-service/.env.example

# Server Configuration
PORT=3006
NODE_ENV=development

# JWT Configuration (same as other services)
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# Internal Service Authentication
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Google AI Configuration (reuse from analysis-worker)
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
GOOGLE_AI_MODEL=gemini-2.5-flash
AI_TEMPERATURE=0.7

# Service URLs
AUTH_SERVICE_URL=http://localhost:3001
ARCHIVE_SERVICE_URL=http://localhost:3002
ASSESSMENT_SERVICE_URL=http://localhost:3003

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
EVENTS_EXCHANGE_NAME=atma_events_exchange
EVENTS_QUEUE_NAME_CHATBOT=analysis_events_chatbot

# Redis Configuration (for session management)
REDIS_URL=redis://localhost:6379

# Database Configuration (optional)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=atma_chatbot
DB_USER=atma_user
DB_PASSWORD=atma_password

# Rate Limiting
CHAT_RATE_LIMIT_WINDOW=60000
CHAT_RATE_LIMIT_MAX=30

# Logging
LOG_LEVEL=info
LOG_FILE=logs/chatbot-service.log

# CORS Configuration
CORS_ORIGIN=*
```

### 6. Package.json

```json
{
  "name": "atma-chatbot-service",
  "version": "1.0.0",
  "description": "Chatbot Service for ATMA Backend - AI-powered career counseling chat",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/server.js",
    "dev": "nodemon src/server.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix"
  },
  "dependencies": {
    "express": "^4.18.2",
    "socket.io": "^4.7.2",
    "@google/genai": "^0.1.0",
    "amqplib": "^0.10.3",
    "redis": "^4.6.7",
    "jsonwebtoken": "^9.0.1",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "express-rate-limit": "^6.8.1",
    "joi": "^17.9.2",
    "winston": "^3.10.0",
    "dotenv": "^16.3.1",
    "mysql2": "^3.6.0",
    "sequelize": "^6.32.1"
  },
  "devDependencies": {
    "nodemon": "^3.0.1",
    "jest": "^29.6.1",
    "eslint": "^8.45.0",
    "supertest": "^6.3.3"
  },
  "keywords": ["chatbot", "ai", "career", "counseling", "microservice"],
  "author": "ATMA Team",
  "license": "MIT"
}
```

## 🔧 Development Setup

### 1. Quick Start Commands

```bash
# Clone and setup
git clone <repository>
cd atma-backend

# Create chatbot service
mkdir chatbot-service
cd chatbot-service

# Initialize service
npm init -y
npm install express socket.io @google/genai amqplib redis jsonwebtoken cors helmet express-rate-limit joi winston dotenv

# Create directory structure
mkdir -p src/{config,controllers,services,middleware,models,routes,utils,websocket}

# Copy environment file
cp .env.example .env

# Start development
npm run dev
```

### 2. Integration Testing

```javascript
// chatbot-service/tests/integration/chat.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('Chat API Integration', () => {
  let authToken;
  let conversationId;

  beforeAll(async () => {
    // Get auth token from auth service
    const authResponse = await request('http://localhost:3001')
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });

    authToken = authResponse.body.data.token;
  });

  test('should start new conversation', async () => {
    const response = await request(app)
      .post('/api/chat/conversations')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        title: 'Career Guidance Chat'
      });

    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
    expect(response.body.data.conversationId).toBeDefined();

    conversationId = response.body.data.conversationId;
  });

  test('should send message and get response', async () => {
    const response = await request(app)
      .post(`/api/chat/conversations/${conversationId}/messages`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        message: 'Apa saran karir untuk saya berdasarkan hasil analisis?'
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data.response).toBeDefined();
    expect(response.body.data.response.length).toBeGreaterThan(0);
  });
});
```

## 📈 Performance Optimization

### 1. Caching Strategy

```javascript
// chatbot-service/src/services/cacheService.js
const redis = require('redis');
const client = redis.createClient(process.env.REDIS_URL);

class CacheService {
  async cachePersonaData(userId, personaData, ttl = 3600) {
    await client.setex(`persona:${userId}`, ttl, JSON.stringify(personaData));
  }

  async getCachedPersonaData(userId) {
    const cached = await client.get(`persona:${userId}`);
    return cached ? JSON.parse(cached) : null;
  }

  async cacheAIResponse(prompt, response, ttl = 1800) {
    const key = `ai_response:${this.hashPrompt(prompt)}`;
    await client.setex(key, ttl, response);
  }

  hashPrompt(prompt) {
    return require('crypto').createHash('md5').update(prompt).digest('hex');
  }
}

module.exports = new CacheService();
```

### 2. Connection Pooling

```javascript
// chatbot-service/src/config/database.js
const { Sequelize } = require('sequelize');

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: 'mysql',
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    logging: process.env.NODE_ENV === 'development' ? console.log : false
  }
);

module.exports = sequelize;
```

## 🎯 Kesimpulan

Chatbot Service yang diusulkan akan:

1. **Memanfaatkan infrastruktur yang ada** - Menggunakan Google AI, RabbitMQ, dan pola arsitektur yang sudah established
2. **Scalable dan maintainable** - Standalone service yang dapat di-scale independent
3. **Terintegrasi penuh** - Menggunakan data persona dari Archive Service dan menerima updates via RabbitMQ events
4. **Real-time dan responsive** - WebSocket support untuk chat experience yang smooth
5. **Secure dan robust** - Mengikuti security patterns yang sudah ada di ATMA backend

**Rekomendasi implementasi**: Mulai dengan MVP (Phase 1) untuk validasi konsep, kemudian iteratively improve dengan features advanced dan optimization.

Apakah Anda ingin saya buatkan implementasi detail untuk komponen tertentu atau ada pertanyaan spesifik tentang arsitektur ini?
